#!/usr/bin/env python3
"""
测试数据准备过程
"""
import os
import sys

def test_data_preparation():
    """测试数据准备"""
    print("="*60)
    print("测试数据准备过程")
    print("="*60)
    
    # 1. 检查原始数据是否存在
    original_data = "data/matbench/matbench_mp_e_form_processed.pkl"
    if not os.path.exists(original_data):
        print(f"ERROR: 原始数据文件不存在: {original_data}")
        return False
    
    print(f"OK: 原始数据文件存在: {original_data}")
    
    # 2. 运行数据准备脚本
    print("\n运行数据准备脚本...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "prepare_matbench_data.py"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"ERROR: 数据准备脚本失败")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
        
        print("OK: 数据准备脚本执行成功")
        
    except Exception as e:
        print(f"ERROR: 执行数据准备脚本时出错: {e}")
        return False
    
    # 3. 检查测试数据是否生成
    test_data = "data/matbench/matbench_mp_e_form_test_200.pkl"
    if not os.path.exists(test_data):
        print(f"ERROR: 测试数据文件未生成: {test_data}")
        return False
    
    print(f"OK: 测试数据文件已生成: {test_data}")
    
    # 4. 验证测试数据
    print("\n验证测试数据...")
    try:
        import pickle
        with open(test_data, 'rb') as f:
            data = pickle.load(f)
        
        if 'graphs' not in data or 'props' not in data:
            print(f"ERROR: 测试数据格式不正确，缺少必要的键")
            return False
        
        graphs = data['graphs']
        props = data['props']
        
        print(f"OK: 测试数据包含 {len(graphs)} 个图")
        print(f"OK: 属性数据类型: {type(props)}")
        
        if isinstance(props, dict):
            for key, values in props.items():
                print(f"OK: 属性 '{key}' 包含 {len(values)} 个值")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 验证测试数据时出错: {e}")
        return False

def test_dataset_class():
    """测试数据集类"""
    print("\n" + "="*60)
    print("测试数据集类")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建数据集实例
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=5,  # 只测试5个样本
            overwrite=True,
        )
        
        print(f"OK: 数据集创建成功，包含 {len(dataset)} 个样本")
        
        # 测试获取单个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"OK: 成功获取样本，键: {list(sample.keys())}")
            
            if 'graph' in sample:
                print(f"OK: 样本包含图数据")
            
            if 'e_form' in sample:
                print(f"OK: 样本包含e_form属性")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 测试数据集类时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("MatBench数据准备和数据集测试")
    
    # 测试数据准备
    if not test_data_preparation():
        print("\nFAIL: 数据准备测试失败")
        return False
    
    # 测试数据集类
    if not test_dataset_class():
        print("\nFAIL: 数据集类测试失败")
        return False
    
    print("\n" + "="*60)
    print("SUCCESS: 所有测试通过！")
    print("="*60)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
