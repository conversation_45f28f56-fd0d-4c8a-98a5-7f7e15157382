#!/usr/bin/env python3
"""
测试Transform修复
"""
import os
import sys

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_transform_import():
    """测试Transform导入"""
    print("="*60)
    print("测试Transform导入")
    print("="*60)
    
    try:
        from ppmat.datasets.transform import ComformerCompatTransform
        print("OK: ComformerCompatTransform 导入成功")
        
        # 测试实例化
        transform = ComformerCompatTransform()
        print("OK: ComformerCompatTransform 实例化成功")
        
        # 测试调用
        test_data = {
            'test_key': 'test_value',
            'id': 0
        }
        
        result = transform(test_data)
        print("OK: ComformerCompatTransform 调用成功")
        print(f"输入: {test_data}")
        print(f"输出: {result}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Transform导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_dataloader():
    """测试简单的数据加载器"""
    print("\n" + "="*60)
    print("测试简单数据加载器")
    print("="*60)
    
    try:
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 最简单的配置（不使用transforms）
        config = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 5,  # 极小样本
                    "overwrite": False,
                },
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 2,
                },
            },
            "num_workers": 0,
        }
        
        print("构建数据加载器...")
        dataloader = build_dataloader(config)
        
        print("OK: 数据加载器构建成功")
        
        # 测试获取一个批次
        for batch in dataloader:
            print("OK: 成功获取批次")
            print(f"批次键: {list(batch.keys())}")
            
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                else:
                    print(f"  {key}: type={type(value)}")
            break
        
        return True
        
    except Exception as e:
        print(f"ERROR: 简单数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n" + "="*60)
    print("测试配置文件加载")
    print("="*60)
    
    try:
        import yaml
        
        config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
        
        if not os.path.exists(config_path):
            print(f"ERROR: 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("OK: 配置文件加载成功")
        
        # 检查数据集配置
        dataset_config = config['Dataset']['train']['dataset']['__init_params__']
        
        if 'transforms' in dataset_config:
            print(f"WARNING: 配置中仍包含transforms: {dataset_config['transforms']}")
        else:
            print("OK: 配置中已移除transforms")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 配置文件加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Transform修复测试")
    
    tests = [
        test_transform_import,
        test_simple_dataloader,
        test_config_loading,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\nFAIL: {test_func.__name__} 失败")
        except Exception as e:
            print(f"ERROR: {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"Transform修复测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("SUCCESS: Transform修复成功！")
        print("现在可以运行: python test_matbench_training_simple.py")
        return True
    else:
        print("FAIL: Transform修复失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
