#!/usr/bin/env python3
"""
测试Comformer Transform修复
"""
import os
import sys

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_transform():
    """测试Transform"""
    print("="*60)
    print("测试ComformerCompatTransform")
    print("="*60)
    
    try:
        from ppmat.datasets.transform import ComformerCompatTransform
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建transform
        transform = ComformerCompatTransform()
        print("OK: ComformerCompatTransform 创建成功")
        
        # 创建数据集（不使用transform）
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=1,
            overwrite=False,
        )
        
        print(f"OK: 数据集创建成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            # 获取原始样本
            original_sample = dataset[0]
            print(f"原始样本键: {list(original_sample.keys())}")
            
            if 'graph' in original_sample:
                original_graph = original_sample['graph']
                print(f"原始图类型: {type(original_graph)}")
                
                # 检查原始图结构
                if hasattr(original_graph, 'node_feat'):
                    if isinstance(original_graph.node_feat, dict):
                        print(f"原始node_feat键: {list(original_graph.node_feat.keys())}")
                    else:
                        print(f"原始node_feat类型: {type(original_graph.node_feat)}")
                
                # 应用transform
                transformed_sample = transform(original_sample)
                print("OK: Transform应用成功")
                
                # 检查转换后的图结构
                transformed_graph = transformed_sample['graph']
                print(f"转换后图类型: {type(transformed_graph)}")
                
                if hasattr(transformed_graph, 'node_feat'):
                    if isinstance(transformed_graph.node_feat, dict):
                        print(f"转换后node_feat键: {list(transformed_graph.node_feat.keys())}")
                        if 'node_feat' in transformed_graph.node_feat:
                            node_feat = transformed_graph.node_feat['node_feat']
                            print(f"  node_feat: shape={node_feat.shape}, dtype={node_feat.dtype}")
                        else:
                            print("  ERROR: 缺少node_feat键")
                    else:
                        print(f"转换后node_feat类型: {type(transformed_graph.node_feat)}")
                
                if hasattr(transformed_graph, 'edge_feat'):
                    if isinstance(transformed_graph.edge_feat, dict):
                        print(f"转换后edge_feat键: {list(transformed_graph.edge_feat.keys())}")
                        for key in ['r', 'nei']:
                            if key in transformed_graph.edge_feat:
                                feat = transformed_graph.edge_feat[key]
                                print(f"  {key}: shape={feat.shape}, dtype={feat.dtype}")
                            else:
                                print(f"  ERROR: 缺少{key}键")
                    else:
                        print(f"转换后edge_feat类型: {type(transformed_graph.edge_feat)}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Transform测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataset_with_transform():
    """测试带Transform的数据集"""
    print("\n" + "="*60)
    print("测试带Transform的数据集")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.datasets.transform import ComformerCompatTransform
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建带transform的数据集
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=2,
            overwrite=False,
            transforms=ComformerCompatTransform(),
        )
        
        print(f"OK: 带Transform的数据集创建成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"样本键: {list(sample.keys())}")
            
            if 'graph' in sample:
                graph = sample['graph']
                print(f"图类型: {type(graph)}")
                
                # 验证Comformer所需的结构
                required_checks = [
                    ('node_feat', 'node_feat'),
                    ('edge_feat', 'r'),
                    ('edge_feat', 'nei'),
                ]
                
                all_good = True
                for attr, key in required_checks:
                    if hasattr(graph, attr):
                        attr_value = getattr(graph, attr)
                        if isinstance(attr_value, dict) and key in attr_value:
                            feat = attr_value[key]
                            print(f"OK: {attr}['{key}'] 存在, shape={feat.shape}")
                        else:
                            print(f"ERROR: {attr}['{key}'] 不存在")
                            all_good = False
                    else:
                        print(f"ERROR: {attr} 属性不存在")
                        all_good = False
                
                if all_good:
                    print("SUCCESS: 图结构符合Comformer要求")
                else:
                    print("ERROR: 图结构不符合Comformer要求")
                    return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: 带Transform的数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Comformer Transform修复测试")
    
    tests = [
        test_transform,
        test_dataset_with_transform,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\nFAIL: {test_func.__name__} 失败")
        except Exception as e:
            print(f"ERROR: {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"Transform修复测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("SUCCESS: Comformer Transform修复成功！")
        print("现在可以重新运行训练:")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        return True
    else:
        print("FAIL: Transform修复失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
