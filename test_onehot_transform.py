#!/usr/bin/env python3
"""
测试One-hot Transform
"""
import os
import sys
import numpy as np
import paddle

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_onehot_transform():
    """测试One-hot Transform"""
    print("="*60)
    print("测试One-hot ComformerCompatTransform")
    print("="*60)
    
    try:
        from ppmat.datasets.transform import ComformerCompatTransform
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建transform
        transform = ComformerCompatTransform()
        print("OK: ComformerCompatTransform 创建成功")
        
        # 创建数据集（不使用transform）
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=1,
            overwrite=False,
        )
        
        print(f"OK: 数据集创建成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            # 获取原始样本
            original_sample = dataset[0]
            original_graph = original_sample['graph']
            
            print(f"原始atom_types: {original_graph.node_feat['atom_types']}")
            print(f"原始atom_types shape: {original_graph.node_feat['atom_types'].shape}")
            
            # 应用transform
            transformed_sample = transform(original_sample)
            transformed_graph = transformed_sample['graph']
            
            print("OK: Transform应用成功")
            
            # 检查转换后的特征
            if 'node_feat' in transformed_graph.node_feat:
                node_feat = transformed_graph.node_feat['node_feat']
                print(f"转换后node_feat shape: {node_feat.shape}")
                print(f"转换后node_feat dtype: {node_feat.dtype}")
                print(f"转换后node_feat前几行:\n{node_feat[:2]}")
                
                # 验证one-hot编码
                if node_feat.shape[1] == 92:
                    print("OK: node_feat维度正确 (92维)")
                    
                    # 检查是否是有效的one-hot编码
                    row_sums = paddle.sum(node_feat, axis=1)
                    print(f"每行和: {row_sums}")
                    
                    if paddle.allclose(row_sums, paddle.ones_like(row_sums)):
                        print("OK: one-hot编码正确")
                    else:
                        print("WARNING: one-hot编码可能有问题")
                else:
                    print(f"ERROR: node_feat维度不正确，期望92，实际{node_feat.shape[1]}")
                    return False
            else:
                print("ERROR: 缺少node_feat")
                return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: One-hot Transform测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_compatibility():
    """测试模型兼容性"""
    print("\n" + "="*60)
    print("测试模型兼容性")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.datasets.transform import ComformerCompatTransform
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建数据加载器配置
        config = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 4,  # 小样本测试
                    "overwrite": False,
                    "transforms": ComformerCompatTransform(),
                },
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 2,
                },
            },
            "num_workers": 0,
        }
        
        print("构建数据加载器...")
        dataloader = build_dataloader(config)
        
        print("OK: 数据加载器构建成功")
        
        # 测试获取一个批次
        for batch in dataloader:
            print("OK: 成功获取批次")
            
            # 检查批次数据
            if 'graph' in batch:
                graph = batch['graph']
                print(f"批次图类型: {type(graph)}")
                
                if hasattr(graph, 'node_feat') and 'node_feat' in graph.node_feat:
                    node_feat = graph.node_feat['node_feat']
                    print(f"批次node_feat shape: {node_feat.shape}")
                    print(f"批次node_feat dtype: {node_feat.dtype}")
                    
                    if node_feat.shape[-1] == 92:
                        print("OK: 批次数据维度正确")
                        return True
                    else:
                        print(f"ERROR: 批次数据维度不正确: {node_feat.shape}")
                        return False
                else:
                    print("ERROR: 批次数据缺少node_feat")
                    return False
            else:
                print("ERROR: 批次数据缺少graph")
                return False
            
            break  # 只测试第一个批次
        
        return True
        
    except Exception as e:
        print(f"ERROR: 模型兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("One-hot Transform测试")
    
    tests = [
        test_onehot_transform,
        test_model_compatibility,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\nFAIL: {test_func.__name__} 失败")
        except Exception as e:
            print(f"ERROR: {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"One-hot Transform测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("SUCCESS: One-hot Transform修复成功！")
        print("现在可以重新运行训练:")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        return True
    else:
        print("FAIL: One-hot Transform修复失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
