#!/usr/bin/env python3
"""
训练监控脚本
"""
import os
import sys
import time
import glob
import re
from datetime import datetime

def find_latest_output_dir():
    """查找最新的输出目录"""
    pattern = "output/comformer_matbench_mp_e_form_test*"
    dirs = glob.glob(pattern)
    
    if not dirs:
        return None
    
    # 按修改时间排序，返回最新的
    latest_dir = max(dirs, key=os.path.getmtime)
    return latest_dir

def monitor_log_file(log_path, tail_lines=20):
    """监控日志文件"""
    if not os.path.exists(log_path):
        print(f"日志文件不存在: {log_path}")
        return
    
    print(f"监控日志文件: {log_path}")
    print("="*60)
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 显示最后几行
            if lines:
                print("最新日志内容:")
                print("-" * 40)
                for line in lines[-tail_lines:]:
                    print(line.rstrip())
                print("-" * 40)
            else:
                print("日志文件为空")
                
    except Exception as e:
        print(f"读取日志文件时出错: {e}")

def extract_training_metrics(log_path):
    """从日志中提取训练指标"""
    if not os.path.exists(log_path):
        return None
    
    metrics = {
        'epochs': [],
        'train_loss': [],
        'val_loss': [],
        'train_metric': [],
        'val_metric': []
    }
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 提取epoch信息
            epoch_pattern = r'Epoch\s+(\d+)'
            epochs = re.findall(epoch_pattern, content)
            
            # 提取损失信息
            loss_pattern = r'loss:\s*([\d.]+)'
            losses = re.findall(loss_pattern, content)
            
            # 提取指标信息
            metric_pattern = r'e_form:\s*([\d.]+)'
            metric_values = re.findall(metric_pattern, content)
            
            print(f"找到 {len(epochs)} 个epoch记录")
            print(f"找到 {len(losses)} 个损失值")
            print(f"找到 {len(metric_values)} 个指标值")
            
            return {
                'epochs': len(epochs),
                'latest_loss': losses[-1] if losses else None,
                'latest_metric': metric_values[-1] if metric_values else None
            }
            
    except Exception as e:
        print(f"提取训练指标时出错: {e}")
        return None

def check_training_status():
    """检查训练状态"""
    print("检查训练状态...")
    print("="*60)
    
    # 查找最新的输出目录
    latest_dir = find_latest_output_dir()
    
    if not latest_dir:
        print("未找到训练输出目录")
        return False
    
    print(f"最新输出目录: {latest_dir}")
    
    # 检查日志文件
    log_file = os.path.join(latest_dir, "run.log")
    if os.path.exists(log_file):
        print(f"找到日志文件: {log_file}")
        
        # 获取文件修改时间
        mtime = os.path.getmtime(log_file)
        mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
        print(f"日志最后修改时间: {mtime_str}")
        
        # 检查是否最近有更新（5分钟内）
        current_time = time.time()
        if current_time - mtime < 300:  # 5分钟
            print("状态: 训练可能正在进行中")
        else:
            print("状态: 训练可能已停止或完成")
        
        # 监控日志
        monitor_log_file(log_file)
        
        # 提取指标
        metrics = extract_training_metrics(log_file)
        if metrics:
            print("\n训练进度:")
            print(f"  已完成epoch数: {metrics['epochs']}")
            if metrics['latest_loss']:
                print(f"  最新损失: {metrics['latest_loss']}")
            if metrics['latest_metric']:
                print(f"  最新指标: {metrics['latest_metric']}")
        
        return True
    else:
        print("未找到日志文件")
        return False

def monitor_training_realtime(interval=30):
    """实时监控训练"""
    print(f"开始实时监控训练 (每{interval}秒更新一次)")
    print("按 Ctrl+C 停止监控")
    print("="*60)
    
    try:
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
            print(f"训练监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*60)
            
            check_training_status()
            
            print(f"\n等待 {interval} 秒后更新...")
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    """主函数"""
    print("MatBench训练监控")
    print("="*60)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "realtime":
            monitor_training_realtime()
        elif sys.argv[1] == "status":
            check_training_status()
        else:
            print("用法:")
            print("  python monitor_training.py status    # 检查训练状态")
            print("  python monitor_training.py realtime  # 实时监控训练")
    else:
        # 默认检查状态
        check_training_status()

if __name__ == "__main__":
    main()
