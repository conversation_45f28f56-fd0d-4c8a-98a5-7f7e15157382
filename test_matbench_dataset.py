#!/usr/bin/env python3
"""
测试MatBench数据集的脚本
"""
import os
import sys
import traceback

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

from ppmat.utils import logger
from ppmat.datasets import build_dataloader
from ppmat.datasets import set_signal_handlers


def test_matbench_dataset_basic():
    """基础数据集测试"""
    print("="*60)
    print("测试1: 基础MatBench数据集加载")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 基础配置
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=10,  # 只测试10个样本
            build_structure_cfg={
                "format": "dict",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            },
            overwrite=True,  # 强制重新构建缓存
        )
        
        print(f"数据集长度: {len(dataset)}")
        
        # 测试获取单个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"样本键: {list(sample.keys())}")
            
            for key, value in sample.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                else:
                    print(f"  {key}: {type(value)}, value={value}")
        
        print("✓ 基础数据集测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基础数据集测试失败: {e}")
        traceback.print_exc()
        return False


def test_matbench_dataset_with_graph():
    """带图构建的数据集测试"""
    print("="*60)
    print("测试2: 带图构建的MatBench数据集")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 带图构建的配置
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=5,  # 只测试5个样本
            build_structure_cfg={
                "format": "dict",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            },
            build_graph_cfg={
                "__class_name__": "FindPointsInSpheres",
                "__init_params__": {
                    "cutoff": 4.0,
                    "num_cpus": 1,
                },
            },
            overwrite=True,
        )
        
        print(f"数据集长度: {len(dataset)}")
        
        # 测试获取单个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"样本键: {list(sample.keys())}")
            
            if 'graph' in sample:
                graph = sample['graph']
                print(f"图类型: {type(graph)}")
                if hasattr(graph, 'keys'):
                    print(f"图键: {list(graph.keys())}")
                    for key, value in graph.items():
                        if hasattr(value, 'shape'):
                            print(f"  {key}: shape={value.shape}")
                        else:
                            print(f"  {key}: {type(value)}")
        
        print("✓ 图构建测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 图构建测试失败: {e}")
        traceback.print_exc()
        return False


def test_matbench_dataloader():
    """测试数据加载器"""
    print("="*60)
    print("测试3: MatBench数据加载器")
    print("="*60)
    
    try:
        # 数据加载器配置
        data_cfg = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 20,
                    "build_structure_cfg": {
                        "format": "dict",
                        "primitive": False,
                        "niggli": True,
                        "num_cpus": 1,
                    },
                    "build_graph_cfg": {
                        "__class_name__": "FindPointsInSpheres",
                        "__init_params__": {
                            "cutoff": 4.0,
                            "num_cpus": 1,
                        },
                    },
                    "overwrite": True,
                },
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 4,
                },
            },
            "num_workers": 0,
        }
        
        dataloader = build_dataloader(data_cfg)
        print(f"数据加载器类型: {type(dataloader)}")
        
        # 测试迭代
        batch_count = 0
        for batch in dataloader:
            batch_count += 1
            print(f"批次 {batch_count}:")
            
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                else:
                    print(f"  {key}: {type(value)}")
            
            if batch_count >= 2:  # 只测试前2个批次
                break
        
        print(f"✓ 数据加载器测试通过，处理了{batch_count}个批次")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        traceback.print_exc()
        return False


def test_matbench_split_dataset():
    """测试数据集分割"""
    print("="*60)
    print("测试4: MatBench数据集分割")
    print("="*60)
    
    try:
        # 带分割的数据加载器配置
        data_cfg = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 30,
                    "build_structure_cfg": {
                        "format": "dict",
                        "primitive": False,
                        "niggli": True,
                        "num_cpus": 1,
                    },
                    "overwrite": True,
                },
            },
            "split_dataset_ratio": {
                "train": 0.7,
                "val": 0.2,
                "test": 0.1,
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 4,
                },
            },
            "num_workers": 0,
        }
        
        dataloader_dict = build_dataloader(data_cfg)
        print(f"数据加载器字典键: {list(dataloader_dict.keys())}")
        
        for split_name, dataloader in dataloader_dict.items():
            if dataloader is not None:
                batch_count = 0
                for batch in dataloader:
                    batch_count += 1
                    if batch_count >= 1:  # 只测试第一个批次
                        break
                print(f"  {split_name}: {batch_count} 批次")
            else:
                print(f"  {split_name}: None")
        
        print("✓ 数据集分割测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据集分割测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    # 初始化日志
    logger_path = "test_matbench_dataset.log"
    logger.init_logger(log_file=logger_path)
    logger.info("开始MatBench数据集测试")
    
    set_signal_handlers()
    
    # 运行所有测试
    tests = [
        test_matbench_dataset_basic,
        test_matbench_dataset_with_graph,
        test_matbench_dataloader,
        test_matbench_split_dataset,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            traceback.print_exc()
        print()
    
    print("="*60)
    print(f"测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("🎉 所有测试通过！MatBench数据集适配成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
