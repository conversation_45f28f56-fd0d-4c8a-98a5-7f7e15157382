#!/usr/bin/env python3
"""
脚本用于检查matbench数据集的结构
"""
import pickle
import os
import sys

def inspect_matbench_data():
    """检查matbench数据集的结构"""
    data_path = "data/matbench/matbench_mp_e_form_processed.pkl"
    
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return
    
    print(f"正在加载数据文件: {data_path}")
    
    try:
        with open(data_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"字典键: {list(data.keys())}")
            for key, value in data.items():
                print(f"  {key}: {type(value)}, 长度: {len(value) if hasattr(value, '__len__') else 'N/A'}")
                if hasattr(value, '__len__') and len(value) > 0:
                    print(f"    第一个元素类型: {type(value[0])}")
                    if hasattr(value[0], 'keys') and callable(getattr(value[0], 'keys')):
                        print(f"    第一个元素键: {list(value[0].keys())}")
                    print(f"    前3个元素: {value[:3]}")
                print()
        
        elif isinstance(data, list):
            print(f"列表长度: {len(data)}")
            if len(data) > 0:
                print(f"第一个元素类型: {type(data[0])}")
                if hasattr(data[0], 'keys') and callable(getattr(data[0], 'keys')):
                    print(f"第一个元素键: {list(data[0].keys())}")
                print(f"前3个元素: {data[:3]}")
        
        else:
            print(f"数据内容: {data}")
            
    except Exception as e:
        print(f"加载数据时出错: {e}")

if __name__ == "__main__":
    inspect_matbench_data()
