#!/usr/bin/env python3
"""
脚本用于检查matbench数据集的结构
"""
import pickle
import os
import sys

def inspect_matbench_data():
    """检查matbench数据集的结构"""
    data_path = "data/matbench/matbench_mp_e_form_processed.pkl"
    
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return
    
    print(f"正在加载数据文件: {data_path}")
    
    try:
        with open(data_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"字典键: {list(data.keys())}")
            for key, value in data.items():
                print(f"  {key}: {type(value)}, 长度: {len(value) if hasattr(value, '__len__') else 'N/A'}")

                if key == 'graphs' and isinstance(value, dict):
                    # graphs是字典，键可能是索引
                    graph_keys = list(value.keys())
                    print(f"    图键数量: {len(graph_keys)}")
                    print(f"    前5个图键: {graph_keys[:5]}")

                    # 检查第一个图
                    if len(graph_keys) > 0:
                        first_key = graph_keys[0]
                        first_graph = value[first_key]
                        print(f"    第一个图类型: {type(first_graph)}")
                        if hasattr(first_graph, 'num_nodes'):
                            print(f"    第一个图节点数: {first_graph.num_nodes}")
                        if hasattr(first_graph, 'num_edges'):
                            print(f"    第一个图边数: {first_graph.num_edges}")

                elif key == 'props' and isinstance(value, dict):
                    # props是字典，检查属性
                    prop_keys = list(value.keys())
                    print(f"    属性键: {prop_keys}")

                    for prop_key, prop_values in value.items():
                        print(f"    {prop_key}: {type(prop_values)}, 长度: {len(prop_values) if hasattr(prop_values, '__len__') else 'N/A'}")
                        if hasattr(prop_values, '__len__') and len(prop_values) > 0:
                            print(f"      前3个值: {prop_values[:3] if hasattr(prop_values, '__getitem__') else 'N/A'}")

                elif hasattr(value, '__len__') and len(value) > 0:
                    try:
                        if hasattr(value, '__getitem__'):
                            first_item = value[0] if isinstance(value, list) else list(value.values())[0]
                            print(f"    第一个元素类型: {type(first_item)}")
                            if hasattr(first_item, 'keys') and callable(getattr(first_item, 'keys')):
                                print(f"    第一个元素键: {list(first_item.keys())}")
                    except Exception as e:
                        print(f"    检查第一个元素时出错: {e}")
                print()
        
        elif isinstance(data, list):
            print(f"列表长度: {len(data)}")
            if len(data) > 0:
                print(f"第一个元素类型: {type(data[0])}")
                if hasattr(data[0], 'keys') and callable(getattr(data[0], 'keys')):
                    print(f"第一个元素键: {list(data[0].keys())}")
                print(f"前3个元素: {data[:3]}")
        
        else:
            print(f"数据内容: {data}")
            
    except Exception as e:
        print(f"加载数据时出错: {e}")

if __name__ == "__main__":
    inspect_matbench_data()
