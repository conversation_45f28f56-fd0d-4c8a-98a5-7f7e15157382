#!/usr/bin/env python3
"""
预处理MatBench数据集脚本
将原始的matbench数据转换为适合ppmat框架的格式
"""
import pickle
import os
import sys
from pymatgen.core.structure import Structure

def prepare_matbench_data():
    """准备MatBench数据集"""
    
    # 原始数据路径
    original_data_path = "data/matbench/matbench_mp_e_form_processed.pkl"
    
    if not os.path.exists(original_data_path):
        print(f"原始数据文件不存在: {original_data_path}")
        return
    
    print(f"加载原始数据: {original_data_path}")
    
    try:
        with open(original_data_path, 'rb') as f:
            raw_data = pickle.load(f)
        
        print(f"原始数据类型: {type(raw_data)}")
        
        # 检查数据结构
        if isinstance(raw_data, dict):
            print(f"数据键: {list(raw_data.keys())}")

            # 实际数据格式为 {'graphs': {...}, 'props': {...}}
            if 'graphs' in raw_data and 'props' in raw_data:
                graphs = raw_data['graphs']
                props = raw_data['props']

                print(f"图数量: {len(graphs)}")
                print(f"属性数据: {type(props)}")

                if isinstance(props, dict):
                    print(f"属性键: {list(props.keys())}")

                    # 查找e_form相关的属性
                    e_form_key = None
                    for key in props.keys():
                        if 'e_form' in key.lower() or 'formation' in key.lower():
                            e_form_key = key
                            break

                    if e_form_key is None:
                        # 如果没找到，使用第一个属性键
                        e_form_key = list(props.keys())[0]
                        print(f"未找到e_form属性，使用第一个属性: {e_form_key}")
                    else:
                        print(f"找到e_form属性: {e_form_key}")

                    e_form_values = props[e_form_key]
                    print(f"e_form数量: {len(e_form_values)}")

                # 获取图的键列表
                graph_keys = list(graphs.keys())
                print(f"图键类型: {type(graph_keys[0]) if graph_keys else 'N/A'}")

                # 检查前几个样本
                print("\n检查前3个样本:")
                for i in range(min(3, len(graph_keys))):
                    key = graph_keys[i]
                    print(f"样本 {i} (键: {key}):")
                    print(f"  图类型: {type(graphs[key])}")
                    if hasattr(graphs[key], 'num_nodes'):
                        print(f"  节点数: {graphs[key].num_nodes}")
                    if hasattr(graphs[key], 'num_edges'):
                        print(f"  边数: {graphs[key].num_edges}")
                    if e_form_key and i < len(e_form_values):
                        print(f"  {e_form_key}值: {e_form_values[i]}")

                # 选择前200个样本用于测试
                max_samples = 200
                selected_keys = graph_keys[:max_samples]

                print(f"\n选择前{len(selected_keys)}个样本用于测试")

                # 构建测试数据
                test_graphs = {key: graphs[key] for key in selected_keys}
                test_props = {e_form_key: e_form_values[:len(selected_keys)]}

                test_data = {
                    'graphs': test_graphs,
                    'props': test_props
                }

                test_data_path = "data/matbench/matbench_mp_e_form_test_200.pkl"
                os.makedirs(os.path.dirname(test_data_path), exist_ok=True)

                with open(test_data_path, 'wb') as f:
                    pickle.dump(test_data, f)

                print(f"测试数据已保存到: {test_data_path}")
                print(f"测试数据包含 {len(selected_keys)} 个样本")
                
            else:
                print("数据格式不符合预期，需要手动检查数据结构")
                # 显示所有键的详细信息
                for key, value in raw_data.items():
                    print(f"键 '{key}': 类型={type(value)}, 长度={len(value) if hasattr(value, '__len__') else 'N/A'}")
                    if hasattr(value, '__len__') and len(value) > 0:
                        print(f"  第一个元素类型: {type(value[0])}")
                        print(f"  前3个元素: {value[:3]}")
        
        elif isinstance(raw_data, list):
            print(f"数据是列表格式，长度: {len(raw_data)}")
            if len(raw_data) > 0:
                print(f"第一个元素类型: {type(raw_data[0])}")
                if hasattr(raw_data[0], 'keys'):
                    print(f"第一个元素键: {list(raw_data[0].keys())}")
        
        else:
            print(f"未知的数据格式: {type(raw_data)}")
            
    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()

def verify_test_data():
    """验证测试数据"""
    test_data_path = "data/matbench/matbench_mp_e_form_test_200.pkl"
    
    if not os.path.exists(test_data_path):
        print(f"测试数据文件不存在: {test_data_path}")
        return
    
    print(f"验证测试数据: {test_data_path}")
    
    try:
        with open(test_data_path, 'rb') as f:
            test_data = pickle.load(f)

        graphs = test_data['graphs']
        props = test_data['props']

        print(f"图数量: {len(graphs)}")
        print(f"属性数据: {type(props)}")

        if isinstance(props, dict):
            for prop_name, prop_values in props.items():
                print(f"{prop_name}数量: {len(prop_values)}")

                # 验证数据完整性
                valid_count = 0
                graph_keys = list(graphs.keys())

                for i, (key, prop_value) in enumerate(zip(graph_keys, prop_values)):
                    graph = graphs[key]
                    if graph is not None and prop_value is not None:
                        valid_count += 1
                    else:
                        print(f"样本 {i} 有缺失数据: graph={graph is not None}, {prop_name}={prop_value is not None}")

                print(f"有效样本数: {valid_count}/{len(graph_keys)}")

                # 显示统计信息
                import numpy as np
                prop_array = np.array([p for p in prop_values if p is not None])
                print(f"{prop_name}统计:")
                print(f"  最小值: {np.min(prop_array):.4f}")
                print(f"  最大值: {np.max(prop_array):.4f}")
                print(f"  平均值: {np.mean(prop_array):.4f}")
                print(f"  标准差: {np.std(prop_array):.4f}")

                # 检查图的基本信息
                print(f"图信息:")
                for i, key in enumerate(list(graphs.keys())[:3]):
                    graph = graphs[key]
                    print(f"  图 {i}: 节点数={graph.num_nodes if hasattr(graph, 'num_nodes') else 'N/A'}, "
                          f"边数={graph.num_edges if hasattr(graph, 'num_edges') else 'N/A'}")
        
    except Exception as e:
        print(f"验证数据时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "verify":
        verify_test_data()
    else:
        prepare_matbench_data()
        print("\n" + "="*50)
        verify_test_data()
