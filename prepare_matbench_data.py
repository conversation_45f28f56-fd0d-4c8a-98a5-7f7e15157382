#!/usr/bin/env python3
"""
预处理MatBench数据集脚本
将原始的matbench数据转换为适合ppmat框架的格式
"""
import pickle
import os
import sys
from pymatgen.core.structure import Structure

def prepare_matbench_data():
    """准备MatBench数据集"""
    
    # 原始数据路径
    original_data_path = "data/matbench/matbench_mp_e_form_processed.pkl"
    
    if not os.path.exists(original_data_path):
        print(f"原始数据文件不存在: {original_data_path}")
        return
    
    print(f"加载原始数据: {original_data_path}")
    
    try:
        with open(original_data_path, 'rb') as f:
            raw_data = pickle.load(f)
        
        print(f"原始数据类型: {type(raw_data)}")
        
        # 检查数据结构
        if isinstance(raw_data, dict):
            print(f"数据键: {list(raw_data.keys())}")
            
            # 假设数据格式为 {'structures': [...], 'e_form': [...]}
            if 'structures' in raw_data and 'e_form' in raw_data:
                structures = raw_data['structures']
                e_form_values = raw_data['e_form']
                
                print(f"结构数量: {len(structures)}")
                print(f"e_form数量: {len(e_form_values)}")
                
                # 检查前几个样本
                print("\n检查前3个样本:")
                for i in range(min(3, len(structures))):
                    print(f"样本 {i}:")
                    print(f"  结构类型: {type(structures[i])}")
                    print(f"  e_form值: {e_form_values[i]}")
                    
                    # 如果是pymatgen Structure对象，显示基本信息
                    if hasattr(structures[i], 'formula'):
                        print(f"  化学式: {structures[i].formula}")
                        print(f"  原子数: {len(structures[i])}")
                
                # 选择前200个样本用于测试
                max_samples = 200
                if len(structures) > max_samples:
                    print(f"\n选择前{max_samples}个样本用于测试")
                    test_structures = structures[:max_samples]
                    test_e_form = e_form_values[:max_samples]
                else:
                    test_structures = structures
                    test_e_form = e_form_values
                
                # 保存测试数据
                test_data = {
                    'structures': test_structures,
                    'e_form': test_e_form
                }
                
                test_data_path = "data/matbench/matbench_mp_e_form_test_200.pkl"
                os.makedirs(os.path.dirname(test_data_path), exist_ok=True)
                
                with open(test_data_path, 'wb') as f:
                    pickle.dump(test_data, f)
                
                print(f"测试数据已保存到: {test_data_path}")
                print(f"测试数据包含 {len(test_structures)} 个样本")
                
            else:
                print("数据格式不符合预期，需要手动检查数据结构")
                # 显示所有键的详细信息
                for key, value in raw_data.items():
                    print(f"键 '{key}': 类型={type(value)}, 长度={len(value) if hasattr(value, '__len__') else 'N/A'}")
                    if hasattr(value, '__len__') and len(value) > 0:
                        print(f"  第一个元素类型: {type(value[0])}")
                        print(f"  前3个元素: {value[:3]}")
        
        elif isinstance(raw_data, list):
            print(f"数据是列表格式，长度: {len(raw_data)}")
            if len(raw_data) > 0:
                print(f"第一个元素类型: {type(raw_data[0])}")
                if hasattr(raw_data[0], 'keys'):
                    print(f"第一个元素键: {list(raw_data[0].keys())}")
        
        else:
            print(f"未知的数据格式: {type(raw_data)}")
            
    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()

def verify_test_data():
    """验证测试数据"""
    test_data_path = "data/matbench/matbench_mp_e_form_test_200.pkl"
    
    if not os.path.exists(test_data_path):
        print(f"测试数据文件不存在: {test_data_path}")
        return
    
    print(f"验证测试数据: {test_data_path}")
    
    try:
        with open(test_data_path, 'rb') as f:
            test_data = pickle.load(f)
        
        structures = test_data['structures']
        e_form_values = test_data['e_form']
        
        print(f"结构数量: {len(structures)}")
        print(f"e_form数量: {len(e_form_values)}")
        
        # 验证数据完整性
        valid_count = 0
        for i, (structure, e_form) in enumerate(zip(structures, e_form_values)):
            if structure is not None and e_form is not None:
                valid_count += 1
            else:
                print(f"样本 {i} 有缺失数据: structure={structure is not None}, e_form={e_form is not None}")
        
        print(f"有效样本数: {valid_count}/{len(structures)}")
        
        # 显示统计信息
        import numpy as np
        e_form_array = np.array([e for e in e_form_values if e is not None])
        print(f"e_form统计:")
        print(f"  最小值: {np.min(e_form_array):.4f}")
        print(f"  最大值: {np.max(e_form_array):.4f}")
        print(f"  平均值: {np.mean(e_form_array):.4f}")
        print(f"  标准差: {np.std(e_form_array):.4f}")
        
    except Exception as e:
        print(f"验证数据时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "verify":
        verify_test_data()
    else:
        prepare_matbench_data()
        print("\n" + "="*50)
        verify_test_data()
