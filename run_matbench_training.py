#!/usr/bin/env python3
"""
MatBench训练脚本
"""
import os
import subprocess
import sys

def main():
    """主函数"""
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    train_script = "property_prediction/train.py"
    
    print("开始MatBench训练...")
    print(f"配置文件: {config_path}")
    print(f"训练脚本: {train_script}")
    
    # 构建命令
    cmd = [sys.executable, train_script, "-c", config_path]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行训练
        result = subprocess.run(cmd, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("训练完成！")
        else:
            print(f"训练失败，返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("训练被用户中断")
        return False
    except Exception as e:
        print(f"训练过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
