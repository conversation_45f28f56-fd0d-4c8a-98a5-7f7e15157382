#!/usr/bin/env python3
"""
运行MatBench数据集的完整测试流程
"""
import os
import sys
import subprocess

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"OK {description} 成功完成")
            return True
        else:
            print(f"FAIL {description} 失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"ERROR 执行 {description} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("MatBench数据集适配完整测试流程")
    print("="*60)
    
    # 测试步骤
    steps = [
        ("python inspect_matbench_data.py", "检查原始数据结构"),
        ("python prepare_matbench_data.py", "准备测试数据"),
        ("python prepare_matbench_data.py verify", "验证测试数据"),
        ("python test_matbench_dataset.py", "测试数据集类"),
    ]
    
    passed = 0
    total = len(steps)
    
    for cmd, description in steps:
        if run_command(cmd, description):
            passed += 1
        else:
            print(f"\nFAIL 测试失败，停止后续测试")
            break
    
    print(f"\n{'='*60}")
    print(f"测试总结: {passed}/{total} 个步骤通过")
    print('='*60)
    
    if passed == total:
        print("SUCCESS 所有测试通过！MatBench数据集适配成功！")
        print("\n下一步可以运行:")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        return True
    else:
        print("FAIL 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
