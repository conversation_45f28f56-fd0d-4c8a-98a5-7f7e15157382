# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import numpy as np
import paddle

__all__ = [
    "ComformerCompatTransform",
]


class ComformerCompatTransform:
    """
    Transform to ensure data compatibility with Comformer model.

    This transform ensures that the graph data format is compatible with the
    Comformer model requirements, specifically:
    - node_feat["node_feat"] for node features
    - edge_feat["r"] for edge distance vectors
    - edge_feat["nei"] for neighbor information
    """

    def __init__(self):
        """Initialize the ComformerCompatTransform."""
        pass

    def __call__(self, data):
        """
        Apply the transform to the data.

        Args:
            data (dict): Input data dictionary containing graph and properties

        Returns:
            dict: Transformed data dictionary
        """
        # Make a copy to avoid modifying the original data
        transformed_data = {}

        for key, value in data.items():
            if key == 'graph':
                # Transform graph data for Comformer compatibility
                transformed_data[key] = self._transform_graph(value)
            elif isinstance(value, np.ndarray):
                # Convert numpy arrays to paddle tensors
                transformed_data[key] = paddle.to_tensor(value)
            else:
                # Keep other data as is
                transformed_data[key] = value

        return transformed_data

    def _transform_graph(self, graph):
        """
        Transform graph data to ensure Comformer compatibility.

        Args:
            graph: Graph data (PGL Graph)

        Returns:
            Transformed graph data with proper node_feat and edge_feat structure
        """
        # If it's not a PGL graph, return as is
        if not (hasattr(graph, 'num_nodes') and hasattr(graph, 'num_edges')):
            return graph

        # Check if the graph already has the required structure
        if (hasattr(graph, 'node_feat') and isinstance(graph.node_feat, dict) and
            'node_feat' in graph.node_feat):
            # Graph already has the correct structure
            return graph

        # Transform the graph to have the required structure
        try:
            # Create a copy of the graph
            import copy
            transformed_graph = copy.deepcopy(graph)

            # Ensure node_feat is a dictionary with 'node_feat' key
            if hasattr(transformed_graph, 'node_feat'):
                if not isinstance(transformed_graph.node_feat, dict):
                    # If node_feat is not a dict, wrap it
                    original_node_feat = transformed_graph.node_feat
                    transformed_graph.node_feat = {'node_feat': original_node_feat}
                elif 'node_feat' not in transformed_graph.node_feat:
                    # If it's a dict but doesn't have 'node_feat' key
                    # Try to use atom_types as node features
                    if 'atom_types' in transformed_graph.node_feat:
                        atom_types = transformed_graph.node_feat['atom_types']
                        # Convert atom types to float32 and ensure proper shape
                        if len(atom_types.shape) == 1:
                            atom_types = atom_types.unsqueeze(-1)
                        transformed_graph.node_feat['node_feat'] = atom_types.cast('float32')
                    elif len(transformed_graph.node_feat) > 0:
                        # Use the first available key
                        first_key = list(transformed_graph.node_feat.keys())[0]
                        first_feat = transformed_graph.node_feat[first_key]
                        if len(first_feat.shape) == 1:
                            first_feat = first_feat.unsqueeze(-1)
                        transformed_graph.node_feat['node_feat'] = first_feat.cast('float32')
                    else:
                        # Create default node features
                        num_nodes = transformed_graph.num_nodes
                        default_feat = paddle.ones([num_nodes, 1], dtype='float32')
                        transformed_graph.node_feat['node_feat'] = default_feat
            else:
                # Create default node features
                num_nodes = transformed_graph.num_nodes
                default_feat = paddle.ones([num_nodes, 1], dtype='float32')
                transformed_graph.node_feat = {'node_feat': default_feat}

            # Ensure edge_feat has the required structure
            if hasattr(transformed_graph, 'edge_feat'):
                if not isinstance(transformed_graph.edge_feat, dict):
                    # If edge_feat is not a dict, create default structure
                    num_edges = transformed_graph.num_edges
                    transformed_graph.edge_feat = {
                        'r': paddle.ones([num_edges, 3], dtype='float32'),
                        'nei': paddle.ones([num_edges, 3], dtype='float32')
                    }
                else:
                    # Ensure required keys exist
                    if 'r' not in transformed_graph.edge_feat:
                        # Try to use bond_vec as r if available
                        if 'bond_vec' in transformed_graph.edge_feat:
                            bond_vec = transformed_graph.edge_feat['bond_vec']
                            transformed_graph.edge_feat['r'] = bond_vec.cast('float32')
                        else:
                            num_edges = transformed_graph.num_edges
                            transformed_graph.edge_feat['r'] = paddle.ones([num_edges, 3], dtype='float32')

                    if 'nei' not in transformed_graph.edge_feat:
                        # Try to use bond_vec as nei if available, otherwise use r
                        if 'bond_vec' in transformed_graph.edge_feat:
                            bond_vec = transformed_graph.edge_feat['bond_vec']
                            transformed_graph.edge_feat['nei'] = bond_vec.cast('float32')
                        elif 'r' in transformed_graph.edge_feat:
                            transformed_graph.edge_feat['nei'] = transformed_graph.edge_feat['r']
                        else:
                            num_edges = transformed_graph.num_edges
                            transformed_graph.edge_feat['nei'] = paddle.ones([num_edges, 3], dtype='float32')
            else:
                # Create default edge features
                num_edges = transformed_graph.num_edges
                transformed_graph.edge_feat = {
                    'r': paddle.ones([num_edges, 3], dtype='float32'),
                    'nei': paddle.ones([num_edges, 3], dtype='float32')
                }

            return transformed_graph

        except Exception as e:
            print(f"Warning: Failed to transform graph structure: {e}")
            return graph
    
    def __repr__(self):
        return f"{self.__class__.__name__}()"
