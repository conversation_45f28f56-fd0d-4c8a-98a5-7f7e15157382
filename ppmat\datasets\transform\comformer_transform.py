# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import numpy as np
import paddle

__all__ = [
    "ComformerCompatTransform",
]


class ComformerCompatTransform:
    """
    Transform to ensure data compatibility with Comformer model.
    
    This transform ensures that the data format is compatible with the
    Comformer model requirements, including proper tensor types and shapes.
    """
    
    def __init__(self):
        """Initialize the ComformerCompatTransform."""
        pass
    
    def __call__(self, data):
        """
        Apply the transform to the data.
        
        Args:
            data (dict): Input data dictionary containing graph and properties
            
        Returns:
            dict: Transformed data dictionary
        """
        # Make a copy to avoid modifying the original data
        transformed_data = {}
        
        for key, value in data.items():
            if key == 'graph':
                # Ensure graph data is properly formatted
                transformed_data[key] = self._transform_graph(value)
            elif isinstance(value, np.ndarray):
                # Convert numpy arrays to paddle tensors
                transformed_data[key] = paddle.to_tensor(value)
            else:
                # Keep other data as is
                transformed_data[key] = value
        
        return transformed_data
    
    def _transform_graph(self, graph):
        """
        Transform graph data to ensure compatibility.
        
        Args:
            graph: Graph data (PGL Graph or dict)
            
        Returns:
            Transformed graph data
        """
        # If it's already a PGL graph, return as is
        if hasattr(graph, 'num_nodes') and hasattr(graph, 'num_edges'):
            return graph
        
        # If it's a dictionary, ensure all values are proper tensors
        if isinstance(graph, dict):
            transformed_graph = {}
            for key, value in graph.items():
                if isinstance(value, np.ndarray):
                    transformed_graph[key] = paddle.to_tensor(value)
                else:
                    transformed_graph[key] = value
            return transformed_graph
        
        return graph
    
    def __repr__(self):
        return f"{self.__class__.__name__}()"
