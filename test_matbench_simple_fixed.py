#!/usr/bin/env python3
"""
修复后的简化MatBench训练测试脚本
"""
import os
import sys
import yaml

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_basic_dataloader():
    """测试基础数据加载器（不分割数据）"""
    print("="*60)
    print("测试基础数据加载器")
    print("="*60)
    
    try:
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 基础配置（不分割数据）
        config = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 10,  # 小样本测试
                    "overwrite": False,
                },
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 4,
                },
            },
            "num_workers": 0,
        }
        
        print("构建数据加载器...")
        dataloader = build_dataloader(config)
        
        print(f"OK: 数据加载器构建成功: {type(dataloader)}")
        
        # 测试获取批次
        batch_count = 0
        for batch in dataloader:
            batch_count += 1
            print(f"批次 {batch_count}:")
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                else:
                    print(f"  {key}: type={type(value)}")
            
            if batch_count >= 2:  # 只测试前2个批次
                break
        
        print(f"OK: 成功处理 {batch_count} 个批次")
        return True
        
    except Exception as e:
        print(f"ERROR: 基础数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_split_dataloader():
    """测试数据分割加载器"""
    print("\n" + "="*60)
    print("测试数据分割加载器")
    print("="*60)
    
    try:
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 分割配置
        config = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 20,
                    "overwrite": False,
                },
            },
            "split_dataset_ratio": {
                "train": 0.8,
                "val": 0.1,
                "test": 0.1,
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size": 4,
                },
            },
            "num_workers": 0,
        }
        
        print("构建分割数据加载器...")
        dataloader_dict = build_dataloader(config)
        
        print(f"OK: 分割数据加载器构建成功: {list(dataloader_dict.keys())}")
        
        # 测试每个分割
        for split_name, dataloader in dataloader_dict.items():
            if dataloader is not None:
                print(f"\n测试 {split_name} 分割:")
                batch_count = 0
                for batch in dataloader:
                    batch_count += 1
                    print(f"  批次 {batch_count}: {len(batch['id'])} 个样本")
                    if batch_count >= 1:  # 只测试第一个批次
                        break
                print(f"  {split_name}: 成功处理 {batch_count} 个批次")
            else:
                print(f"  {split_name}: None")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 数据分割加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yaml_config():
    """测试YAML配置文件"""
    print("\n" + "="*60)
    print("测试YAML配置文件")
    print("="*60)
    
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    try:
        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            print(f"ERROR: 配置文件不存在: {config_path}")
            return False
        
        print(f"OK: 配置文件存在: {config_path}")
        
        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("OK: 配置文件加载成功")
        
        # 检查关键配置项
        required_sections = ['Global', 'Trainer', 'Model', 'Dataset', 'Optimizer', 'Metric']
        for section in required_sections:
            if section not in config:
                print(f"ERROR: 配置文件缺少必要的节: {section}")
                return False
            print(f"OK: 找到配置节: {section}")
        
        # 检查数据集配置
        dataset_config = config['Dataset']
        for split in ['train', 'val', 'test']:
            if split in dataset_config:
                dataset_class = dataset_config[split]['dataset']['__class_name__']
                if dataset_class != 'MatBenchDataset':
                    print(f"ERROR: {split} 数据集类不正确: {dataset_class}")
                    return False
                print(f"OK: {split} 数据集配置正确")
        
        # 检查数据分割比例
        global_config = config['Global']
        if 'split_dataset_ratio' in global_config:
            ratios = global_config['split_dataset_ratio']
            total = sum(ratios.values())
            print(f"数据分割比例: {ratios}, 总和: {total}")
            if abs(total - 1.0) > 1e-6:
                print(f"WARNING: 数据分割比例总和不等于1.0: {total}")
            else:
                print("OK: 数据分割比例正确")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 测试YAML配置文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("修复后的MatBench训练准备测试")
    
    tests = [
        test_basic_dataloader,
        test_split_dataloader,
        test_yaml_config,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\nFAIL: {test_func.__name__} 失败")
                # 继续执行其他测试，不要停止
        except Exception as e:
            print(f"ERROR: {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"训练准备测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed >= 2:  # 至少2个测试通过就认为基本成功
        print("SUCCESS: 训练准备基本完成！")
        print("\n下一步可以运行:")
        print("1. python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        print("2. python complete_matbench_workflow.py")
        return True
    else:
        print("FAIL: 训练准备失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
