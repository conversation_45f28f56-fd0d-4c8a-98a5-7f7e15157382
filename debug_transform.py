#!/usr/bin/env python3
"""
调试Transform问题
"""
import os
import sys
import numpy as np
import paddle

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def debug_transform():
    """调试Transform"""
    print("="*60)
    print("调试ComformerCompatTransform")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建数据集（不使用transform）
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=1,
            overwrite=False,
        )
        
        print(f"数据集创建成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            # 获取原始样本
            sample = dataset[0]
            graph = sample['graph']
            
            print(f"原始图类型: {type(graph)}")
            print(f"图节点数: {graph.num_nodes}")
            print(f"图边数: {graph.num_edges}")
            
            # 详细检查node_feat
            if hasattr(graph, 'node_feat'):
                print(f"\nnode_feat类型: {type(graph.node_feat)}")
                if isinstance(graph.node_feat, dict):
                    print(f"node_feat键: {list(graph.node_feat.keys())}")
                    for key, value in graph.node_feat.items():
                        print(f"  {key}: type={type(value)}, shape={getattr(value, 'shape', 'N/A')}")
                        if key == 'atom_types':
                            print(f"    atom_types内容: {value}")
            
            # 详细检查edge_feat
            if hasattr(graph, 'edge_feat'):
                print(f"\nedge_feat类型: {type(graph.edge_feat)}")
                if isinstance(graph.edge_feat, dict):
                    print(f"edge_feat键: {list(graph.edge_feat.keys())}")
                    for key, value in graph.edge_feat.items():
                        print(f"  {key}: type={type(value)}, shape={getattr(value, 'shape', 'N/A')}")
            
            # 手动创建所需的特征
            print(f"\n手动创建Comformer所需特征:")
            
            # 创建node_feat
            if 'atom_types' in graph.node_feat:
                atom_types = graph.node_feat['atom_types']
                print(f"原始atom_types: type={type(atom_types)}, shape={atom_types.shape}")
                
                # 转换为paddle tensor
                if isinstance(atom_types, np.ndarray):
                    atom_types_tensor = paddle.to_tensor(atom_types, dtype='float32')
                else:
                    atom_types_tensor = atom_types.cast('float32')
                
                # 确保是2D
                if len(atom_types_tensor.shape) == 1:
                    atom_types_tensor = atom_types_tensor.unsqueeze(-1)
                
                print(f"转换后atom_types: shape={atom_types_tensor.shape}, dtype={atom_types_tensor.dtype}")
                
                # 添加到node_feat
                graph.node_feat['node_feat'] = atom_types_tensor
                print("OK: 成功添加node_feat['node_feat']")
            
            # 创建edge_feat
            if 'bond_vec' in graph.edge_feat:
                bond_vec = graph.edge_feat['bond_vec']
                print(f"原始bond_vec: type={type(bond_vec)}, shape={bond_vec.shape}")
                
                # 转换为paddle tensor
                if isinstance(bond_vec, np.ndarray):
                    bond_vec_tensor = paddle.to_tensor(bond_vec, dtype='float32')
                else:
                    bond_vec_tensor = bond_vec.cast('float32')
                
                print(f"转换后bond_vec: shape={bond_vec_tensor.shape}, dtype={bond_vec_tensor.dtype}")
                
                # 添加到edge_feat
                graph.edge_feat['r'] = bond_vec_tensor
                graph.edge_feat['nei'] = bond_vec_tensor  # 使用相同的向量
                print("OK: 成功添加edge_feat['r']和edge_feat['nei']")
            
            # 验证最终结果
            print(f"\n最终验证:")
            required_checks = [
                ('node_feat', 'node_feat'),
                ('edge_feat', 'r'),
                ('edge_feat', 'nei'),
            ]
            
            all_good = True
            for attr, key in required_checks:
                if hasattr(graph, attr):
                    attr_value = getattr(graph, attr)
                    if isinstance(attr_value, dict) and key in attr_value:
                        feat = attr_value[key]
                        print(f"OK: {attr}['{key}'] 存在, shape={feat.shape}, dtype={feat.dtype}")
                    else:
                        print(f"ERROR: {attr}['{key}'] 不存在")
                        all_good = False
                else:
                    print(f"ERROR: {attr} 属性不存在")
                    all_good = False
            
            if all_good:
                print("SUCCESS: 手动转换成功！")
                return True
            else:
                print("ERROR: 手动转换失败")
                return False
        
        return False
        
    except Exception as e:
        print(f"ERROR: 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Transform调试")
    debug_transform()

if __name__ == "__main__":
    main()
