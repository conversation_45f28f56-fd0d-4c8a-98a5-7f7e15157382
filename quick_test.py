#!/usr/bin/env python3
"""
快速测试脚本
"""
import os
import sys

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def main():
    """主函数"""
    print("快速测试MatBench数据集")
    print("="*50)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建数据集
        print("创建MatBench数据集...")
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=5,
            overwrite=False,
        )
        
        print(f"OK: 数据集创建成功，包含 {len(dataset)} 个样本")
        
        # 测试获取样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"OK: 成功获取样本，键: {list(sample.keys())}")
            
            for key, value in sample.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                else:
                    print(f"  {key}: type={type(value)}, value={value}")
        
        print("\n" + "="*50)
        print("SUCCESS: 快速测试通过！")
        print("MatBench数据集工作正常")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"ERROR: 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n下一步可以尝试:")
        print("1. python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        print("2. 或者运行完整工作流程: python complete_matbench_workflow.py")
    sys.exit(0 if success else 1)
