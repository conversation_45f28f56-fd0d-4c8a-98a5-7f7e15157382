#!/usr/bin/env python3
"""
简化的MatBench训练测试脚本
"""
import os
import sys
import yaml

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_config_and_dataloader():
    """测试配置文件和数据加载器"""
    print("="*60)
    print("测试配置文件和数据加载器")
    print("="*60)
    
    try:
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 简化的配置
        config = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 20,  # 小样本测试
                    "overwrite": False,
                    "transforms": [
                        {
                            "__class_name__": "ComformerCompatTransform",
                            "__init_params__": {},
                        }
                    ],
                },
            },
            "split_dataset_ratio": {
                "train": 0.7,
                "val": 0.2,
                "test": 0.1,
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": True,
                    "drop_last": True,
                    "batch_size": 4,
                },
            },
            "num_workers": 0,
        }
        
        print("构建数据加载器...")
        dataloader_dict = build_dataloader(config)
        
        print(f"OK: 数据加载器构建成功: {list(dataloader_dict.keys())}")
        
        # 测试每个数据加载器
        for split_name, dataloader in dataloader_dict.items():
            if dataloader is not None:
                print(f"\n测试 {split_name} 数据加载器:")
                batch_count = 0
                for batch in dataloader:
                    batch_count += 1
                    print(f"  批次 {batch_count}:")
                    for key, value in batch.items():
                        if hasattr(value, 'shape'):
                            print(f"    {key}: shape={value.shape}, dtype={value.dtype}")
                        else:
                            print(f"    {key}: type={type(value)}")
                    
                    if batch_count >= 2:  # 只测试前2个批次
                        break
                
                print(f"  {split_name}: 成功处理 {batch_count} 个批次")
            else:
                print(f"  {split_name}: None")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 测试配置和数据加载器时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yaml_config():
    """测试YAML配置文件"""
    print("\n" + "="*60)
    print("测试YAML配置文件")
    print("="*60)
    
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    try:
        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            print(f"ERROR: 配置文件不存在: {config_path}")
            return False
        
        print(f"OK: 配置文件存在: {config_path}")
        
        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("OK: 配置文件加载成功")
        
        # 检查关键配置项
        required_sections = ['Global', 'Trainer', 'Model', 'Dataset', 'Optimizer', 'Metric']
        for section in required_sections:
            if section not in config:
                print(f"ERROR: 配置文件缺少必要的节: {section}")
                return False
            print(f"OK: 找到配置节: {section}")
        
        # 检查数据集配置
        dataset_config = config['Dataset']
        for split in ['train', 'val', 'test']:
            if split in dataset_config:
                dataset_class = dataset_config[split]['dataset']['__class_name__']
                if dataset_class != 'MatBenchDataset':
                    print(f"ERROR: {split} 数据集类不正确: {dataset_class}")
                    return False
                print(f"OK: {split} 数据集配置正确")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 测试YAML配置文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_training_command():
    """创建最小化的训练命令"""
    print("\n" + "="*60)
    print("创建训练命令")
    print("="*60)
    
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    # 检查训练脚本是否存在
    train_script = "property_prediction/train.py"
    if not os.path.exists(train_script):
        print(f"ERROR: 训练脚本不存在: {train_script}")
        return False
    
    print(f"OK: 训练脚本存在: {train_script}")
    
    # 生成训练命令
    train_command = f"python {train_script} -c {config_path}"
    
    print(f"训练命令: {train_command}")
    
    # 创建训练脚本
    training_script = """#!/usr/bin/env python3
\"\"\"
MatBench训练脚本
\"\"\"
import os
import subprocess
import sys

def main():
    \"\"\"主函数\"\"\"
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    train_script = "property_prediction/train.py"
    
    print("开始MatBench训练...")
    print(f"配置文件: {config_path}")
    print(f"训练脚本: {train_script}")
    
    # 构建命令
    cmd = [sys.executable, train_script, "-c", config_path]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行训练
        result = subprocess.run(cmd, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("训练完成！")
        else:
            print(f"训练失败，返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("训练被用户中断")
        return False
    except Exception as e:
        print(f"训练过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
    
    script_path = "run_matbench_training.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(training_script)
    
    print(f"OK: 训练脚本已创建: {script_path}")
    
    return True

def main():
    """主函数"""
    print("MatBench训练准备测试")
    
    tests = [
        test_config_and_dataloader,
        test_yaml_config,
        create_minimal_training_command,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\nFAIL: {test_func.__name__} 失败")
                break
        except Exception as e:
            print(f"ERROR: {test_func.__name__} 出现异常: {e}")
            break
    
    print("\n" + "="*60)
    print(f"训练准备测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("SUCCESS: 训练准备完成！")
        print("\n下一步可以运行:")
        print("1. python run_matbench_training.py  # 开始训练")
        print("2. 或者直接运行: python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        return True
    else:
        print("FAIL: 训练准备失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
