#!/usr/bin/env python3
"""
检查图数据结构
"""
import os
import sys
import pickle

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def inspect_graph_structure():
    """检查图数据结构"""
    print("检查MatBench图数据结构")
    print("="*60)
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 创建数据集
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_test_200",
            property_name="e_form",
            max_samples=1,  # 只检查一个样本
            overwrite=False,
        )
        
        print(f"数据集长度: {len(dataset)}")
        
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"样本键: {list(sample.keys())}")
            
            if 'graph' in sample:
                graph = sample['graph']
                print(f"\n图类型: {type(graph)}")
                
                # 检查图的属性
                if hasattr(graph, '__dict__'):
                    print(f"图属性: {list(graph.__dict__.keys())}")
                
                # 检查常见的图属性
                attrs_to_check = ['num_nodes', 'num_edges', 'node_feat', 'edge_feat', 'edge_index']
                for attr in attrs_to_check:
                    if hasattr(graph, attr):
                        value = getattr(graph, attr)
                        if hasattr(value, 'shape'):
                            print(f"  {attr}: shape={value.shape}, dtype={value.dtype}")
                        else:
                            print(f"  {attr}: {type(value)}, value={value}")
                    else:
                        print(f"  {attr}: 不存在")
                
                # 如果是PGL图，检查node_feat字典
                if hasattr(graph, 'node_feat') and isinstance(graph.node_feat, dict):
                    print(f"\nnode_feat字典键: {list(graph.node_feat.keys())}")
                    for key, value in graph.node_feat.items():
                        if hasattr(value, 'shape'):
                            print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                        else:
                            print(f"  {key}: {type(value)}")
                
                # 如果是PGL图，检查edge_feat字典
                if hasattr(graph, 'edge_feat') and isinstance(graph.edge_feat, dict):
                    print(f"\nedge_feat字典键: {list(graph.edge_feat.keys())}")
                    for key, value in graph.edge_feat.items():
                        if hasattr(value, 'shape'):
                            print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                        else:
                            print(f"  {key}: {type(value)}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 检查图结构时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def inspect_original_graph():
    """检查原始图数据"""
    print("\n" + "="*60)
    print("检查原始图数据")
    print("="*60)
    
    try:
        # 直接加载原始数据
        test_data_path = "data/matbench/matbench_mp_e_form_test_200.pkl"
        
        with open(test_data_path, 'rb') as f:
            data = pickle.load(f)
        
        graphs = data['graphs']
        first_key = list(graphs.keys())[0]
        first_graph = graphs[first_key]
        
        print(f"原始图类型: {type(first_graph)}")
        print(f"原始图属性: {dir(first_graph)}")
        
        # 检查PGL图的结构
        if hasattr(first_graph, 'node_feat'):
            if isinstance(first_graph.node_feat, dict):
                print(f"原始node_feat键: {list(first_graph.node_feat.keys())}")
                for key, value in first_graph.node_feat.items():
                    if hasattr(value, 'shape'):
                        print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
            else:
                print(f"原始node_feat: {type(first_graph.node_feat)}")
        
        if hasattr(first_graph, 'edge_feat'):
            if isinstance(first_graph.edge_feat, dict):
                print(f"原始edge_feat键: {list(first_graph.edge_feat.keys())}")
                for key, value in first_graph.edge_feat.items():
                    if hasattr(value, 'shape'):
                        print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
            else:
                print(f"原始edge_feat: {type(first_graph.edge_feat)}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: 检查原始图数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    inspect_graph_structure()
    inspect_original_graph()

if __name__ == "__main__":
    main()
