#!/usr/bin/env python3
"""
测试MatBench数据集训练的脚本
"""
import os
import sys
import yaml

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def test_config_loading():
    """测试配置文件加载"""
    print("="*60)
    print("测试配置文件加载")
    print("="*60)
    
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("配置文件加载成功")
        print(f"主要配置项:")
        print(f"  标签名称: {config['Global']['label_names']}")
        print(f"  数据集分割: {config['Global']['split_dataset_ratio']}")
        print(f"  最大训练轮数: {config['Trainer']['max_epochs']}")
        print(f"  批次大小: {config['Dataset']['train']['sampler']['__init_params__']['batch_size']}")
        
        return True
        
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return False


def test_dry_run_training():
    """测试干运行训练（不实际训练，只检查配置）"""
    print("="*60)
    print("测试干运行训练")
    print("="*60)
    
    try:
        # 这里可以添加实际的训练代码测试
        # 由于需要完整的训练环境，这里只做配置验证
        
        from ppmat.datasets import build_dataloader
        from ppmat.utils import logger
        
        # 初始化日志
        logger.init_logger()
        
        # 简化的数据加载器配置
        train_data_cfg = {
            "dataset": {
                "__class_name__": "MatBenchDataset",
                "__init_params__": {
                    "path": "./data/matbench",
                    "task_name": "matbench_mp_e_form_test_200",
                    "property_name": "e_form",
                    "max_samples": 10,  # 极小的样本数用于测试
                    "build_structure_cfg": {
                        "format": "dict",
                        "primitive": False,
                        "niggli": True,
                        "num_cpus": 1,
                    },
                    "build_graph_cfg": {
                        "__class_name__": "ComformerGraphConverter",
                        "__init_params__": {
                            "cutoff": 5.0,
                            "num_cpus": 1,
                            "atom_features": "cgcnn",
                            "max_neighbors": 25,
                        },
                    },
                    "overwrite": True,
                    "transforms": [
                        {
                            "__class_name__": "ComformerCompatTransform",
                            "__init_params__": {},
                        }
                    ],
                },
            },
            "split_dataset_ratio": {
                "train": 0.8,
                "val": 0.2,
                "test": 0.0,
            },
            "sampler": {
                "__class_name__": "BatchSampler",
                "__init_params__": {
                    "shuffle": True,
                    "drop_last": True,
                    "batch_size": 2,
                },
            },
            "num_workers": 0,
        }
        
        print("构建数据加载器...")
        dataloader_dict = build_dataloader(train_data_cfg)
        
        print(f"数据加载器构建成功: {list(dataloader_dict.keys())}")
        
        # 测试一个批次
        train_loader = dataloader_dict['train']
        if train_loader is not None:
            for batch in train_loader:
                print(f"成功加载一个训练批次:")
                for key, value in batch.items():
                    if hasattr(value, 'shape'):
                        print(f"  {key}: {value.shape}")
                    else:
                        print(f"  {key}: {type(value)}")
                break
        
        val_loader = dataloader_dict['val']
        if val_loader is not None:
            for batch in val_loader:
                print(f"成功加载一个验证批次:")
                for key, value in batch.items():
                    if hasattr(value, 'shape'):
                        print(f"  {key}: {value.shape}")
                    else:
                        print(f"  {key}: {type(value)}")
                break
        
        print("✓ 干运行训练测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 干运行训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_minimal_training_script():
    """创建最小化的训练脚本"""
    print("="*60)
    print("创建最小化训练脚本")
    print("="*60)
    
    script_content = '''#!/usr/bin/env python3
"""
最小化的MatBench训练脚本
"""
import os
import sys

# 添加项目路径
__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, __dir__)

def main():
    """主函数"""
    # 这里可以添加实际的训练代码
    # 例如调用 property_prediction/train.py
    
    config_path = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    print(f"使用配置文件: {config_path}")
    print("开始训练...")
    
    # 实际训练命令示例:
    # python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml
    
    print("训练脚本准备就绪")
    print("请运行以下命令开始训练:")
    print(f"python property_prediction/train.py -c {config_path}")

if __name__ == "__main__":
    main()
'''
    
    script_path = "run_matbench_training.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"最小化训练脚本已创建: {script_path}")
    return True


def main():
    """主测试函数"""
    print("MatBench训练测试")
    
    tests = [
        test_config_loading,
        test_dry_run_training,
        create_minimal_training_script,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
        print()
    
    print("="*60)
    print(f"训练测试总结: {passed}/{total} 个测试通过")
    print("="*60)
    
    if passed == total:
        print("🎉 训练测试通过！可以开始实际训练！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
